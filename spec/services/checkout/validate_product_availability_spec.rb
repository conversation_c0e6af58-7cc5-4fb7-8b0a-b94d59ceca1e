require 'rails_helper'

RSpec.describe Checkout::ValidateProductAvailability do
  let(:store) { create(:store) }
  let(:bna_mimoto_store) { create(:store, id: 43) } # BNA_MIMOTO_ID
  let(:columbia_store) { create(:store, id: 46) } # Columbia store

  # Create car categories
  let(:car_category) { create(:category, id: 1762, name: "Autos") }
  let(:auto_tools_category) { create(:category, id: 1800, name: "Automóviles y herramientas") }

  # Create motorcycle categories
  let(:moto_category) { create(:category, id: 1734, name: "Motos") }

  # Create Vehiculos category
  let(:vehiculos_category) { create(:category, id: 3346, name: "Vehiculos") }

  # Create manufacturers
  let(:car_manufacturer) { create(:manufacturer, name: "Toyota") }
  let(:moto_manufacturer) { create(:manufacturer, name: "Honda") }
  let(:vehiculos_manufacturer) { create(:manufacturer, name: "Ford") }

  # Create shops
  let(:shop) { create(:shop) }

  describe '#valid_variant?' do
    context 'car products without stock in regular store' do
      let(:car_product) do
        create(:product, 
               category: car_category, 
               manufacturer: car_manufacturer,
               shop: shop,
               transaction_type: 'reservable')
      end
      let(:car_variant) { create(:variant, product: car_product, quantity: 0) }
      let(:items) { [{ variant_id: car_variant.id }] }
      let(:service) { described_class.new(items, store) }

      it 'should pass validation' do
        service.perform
        
        expect(service.valid).to be true
        expect(service.error).to be_nil
      end
    end

    context 'motorcycle products without stock in regular store' do
      let(:moto_product) do
        create(:product, 
               category: moto_category, 
               manufacturer: moto_manufacturer,
               shop: shop,
               transaction_type: 'reservable')
      end
      let(:moto_variant) { create(:variant, product: moto_product, quantity: 0) }
      let(:items) { [{ variant_id: moto_variant.id }] }
      let(:service) { described_class.new(items, store) }

      it 'should fail validation' do
        service.perform
        
        expect(service.valid).to be false
        expect(service.error).to include("Producto sin stock")
      end
    end

    context 'any product without stock in BNA_MIMOTO store' do
      let(:car_product) do
        create(:product, 
               category: car_category, 
               manufacturer: car_manufacturer,
               shop: shop,
               transaction_type: 'reservable')
      end
      let(:car_variant) { create(:variant, product: car_product, quantity: 0) }
      let(:items) { [{ variant_id: car_variant.id }] }
      let(:service) { described_class.new(items, bna_mimoto_store) }

      it 'should fail validation' do
        service.perform
        
        expect(service.valid).to be false
        expect(service.error).to include("Producto sin stock")
      end
    end

    context 'car products with stock' do
      let(:car_product) do
        create(:product, 
               category: car_category, 
               manufacturer: car_manufacturer,
               shop: shop,
               transaction_type: 'reservable')
      end
      let(:car_variant) { create(:variant, product: car_product, quantity: 5) }
      let(:items) { [{ variant_id: car_variant.id }] }
      let(:service) { described_class.new(items, store) }

      it 'should pass validation' do
        service.perform
        
        expect(service.valid).to be true
        expect(service.error).to be_nil
      end
    end

    context 'motorcycle products with stock' do
      let(:moto_product) do
        create(:product, 
               category: moto_category, 
               manufacturer: moto_manufacturer,
               shop: shop,
               transaction_type: 'reservable')
      end
      let(:moto_variant) { create(:variant, product: moto_product, quantity: 3) }
      let(:items) { [{ variant_id: moto_variant.id }] }
      let(:service) { described_class.new(items, store) }

      it 'should pass validation' do
        service.perform
        
        expect(service.valid).to be true
        expect(service.error).to be_nil
      end
    end

    context 'car product in subcategory' do
      let(:car_subcategory) { create(:category, parent_id: car_category.id, name: "Sedanes") }
      let(:car_product) do
        create(:product, 
               category: car_subcategory, 
               manufacturer: car_manufacturer,
               shop: shop,
               transaction_type: 'reservable')
      end
      let(:car_variant) { create(:variant, product: car_product, quantity: 0) }
      let(:items) { [{ variant_id: car_variant.id }] }
      let(:service) { described_class.new(items, store) }

      it 'should be recognized as car and pass validation without stock' do
        service.perform
        
        expect(service.valid).to be true
        expect(service.error).to be_nil
      end
    end

    context 'product requiring third party codes' do
      let(:car_product) do
        create(:product, 
               category: car_category, 
               manufacturer: car_manufacturer,
               shop: shop,
               transaction_type: 'reservable',
               third_party_code_required: true)
      end
      let(:car_variant) { create(:variant, product: car_product, quantity: 5) }
      let(:items) { [{ variant_id: car_variant.id }] }
      let(:service) { described_class.new(items, store) }

      context 'when no third party codes are available' do
        before do
          allow(ThirdPartyCode).to receive(:where).and_return(double(count: double(positive?: false)))
        end

        it 'should fail validation' do
          service.perform
          
          expect(service.valid).to be false
          expect(service.error).to include("Producto sin stock")
        end
      end

      context 'when third party codes are available' do
        before do
          allow(ThirdPartyCode).to receive(:where).and_return(double(count: double(positive?: true)))
        end

        it 'should pass validation for car products even without stock' do
          car_variant.update(quantity: 0)
          service.perform
          
          expect(service.valid).to be true
          expect(service.error).to be_nil
        end
      end
    end

    context 'Vehiculos products in Columbia store' do
      let(:vehiculos_product) do
        create(:product,
               category: vehiculos_category,
               manufacturer: vehiculos_manufacturer,
               shop: shop,
               transaction_type: 'reservable',
               third_party_code_required: true)
      end
      let(:vehiculos_variant) { create(:variant, product: vehiculos_product, quantity: 5) }
      let(:items) { [{ variant_id: vehiculos_variant.id }] }

      context 'in Columbia store (ID 46)' do
        let(:service) { described_class.new(items, columbia_store) }

        it 'should pass validation even without third party codes' do
          # Mock no third party codes available
          allow(ThirdPartyCode).to receive(:where).and_return(double(count: double(positive?: false)))

          service.perform

          expect(service.valid).to be true
          expect(service.error).to be_nil
        end

        it 'should pass validation with third party codes' do
          # Mock third party codes available
          allow(ThirdPartyCode).to receive(:where).and_return(double(count: double(positive?: true)))

          service.perform

          expect(service.valid).to be true
          expect(service.error).to be_nil
        end
      end

      context 'in regular store (not Columbia)' do
        let(:service) { described_class.new(items, store) }

        it 'should fail validation without third party codes' do
          # Mock no third party codes available
          allow(ThirdPartyCode).to receive(:where).and_return(double(count: double(positive?: false)))

          service.perform

          expect(service.valid).to be false
          expect(service.error).to include("Producto sin stock")
        end

        it 'should pass validation with third party codes' do
          # Mock third party codes available
          allow(ThirdPartyCode).to receive(:where).and_return(double(count: double(positive?: true)))

          service.perform

          expect(service.valid).to be true
          expect(service.error).to be_nil
        end
      end
    end
  end

  describe 'private methods' do
    let(:service) { described_class.new([], store) }

    describe '#car_product?' do
      it 'identifies car products by category ID' do
        car_product = build(:product, category: car_category)
        expect(service.send(:car_product?, car_product)).to be true
      end

      it 'identifies auto tools products as car products' do
        auto_product = build(:product, category: auto_tools_category)
        expect(service.send(:car_product?, auto_product)).to be true
      end

      it 'does not identify motorcycle products as car products' do
        moto_product = build(:product, category: moto_category)
        expect(service.send(:car_product?, moto_product)).to be false
      end
    end

    describe '#requires_stock_validation?' do
      let(:car_product) { build(:product, category: car_category) }
      let(:moto_product) { build(:product, category: moto_category) }
      let(:car_variant) { build(:variant, product: car_product) }
      let(:moto_variant) { build(:variant, product: moto_product) }

      it 'always requires stock validation for BNA_MIMOTO store' do
        bna_service = described_class.new([], bna_mimoto_store)
        expect(bna_service.send(:requires_stock_validation?, car_variant)).to be true
        expect(bna_service.send(:requires_stock_validation?, moto_variant)).to be true
      end

      it 'does not require stock validation for car products in regular stores' do
        expect(service.send(:requires_stock_validation?, car_variant)).to be false
      end

      it 'requires stock validation for motorcycle products in regular stores' do
        expect(service.send(:requires_stock_validation?, moto_variant)).to be true
      end
    end
  end
end
