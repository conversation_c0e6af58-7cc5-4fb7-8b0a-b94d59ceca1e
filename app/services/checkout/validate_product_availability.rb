module Checkout
  class ValidateProductAvailability
    attr_accessor :valid, :error

    def initialize(items, store)
      @items = items
      @store = store
      @valid = true
    end

    def perform # rubocop:disable Metrics/MethodLength
      return if @items.blank?

      @items.each do |item|
        variant = Mkp::Variant.find item[:variant_id]
        if variant.present?
          unless valid_variant?(variant)
            @valid = false
            @error = "Producto sin stock: #{variant.product.title}"
          end
        else
          @valid = false
          @error = 'Error verificando stock'
        end
      end
    end

    def valid_variant?(variant)
      valid = true
      if variant.product.reservable? && variant.product.third_party_code_required && !skip_third_party_code_validation?(variant)
        valid = third_party_code_validation?(variant)
      end

      # For car reservations, stock validation may not be required
      # depending on the business model (cars vs motorcycles)
      stock_valid = if requires_stock_validation?(variant)
                      variant.quantity.positive?
                    else
                      true
                    end

      variant.product.available? && stock_valid && valid
    end

    private

    def requires_stock_validation?(variant)
      # For BNA_MIMOTO_ID store (motorcycles), always require stock validation
      return true if @store&.id == 43 # BNA_MIMOTO_ID

      # For car products, stock validation may not be required
      # as they follow a different business model
      return false if car_product?(variant.product)

      # For all other products (including motorcycles in other stores), require stock
      true
    end

    def car_product?(product)
      # Define car category IDs based on the category analysis
      car_category_ids = [1762, 1800] # "Autos", "Automóviles y herramientas"

      # Check if product belongs to car categories
      return true if car_category_ids.include?(product.category_id)

      # Check if product belongs to car category hierarchy
      category = product.category
      while category&.parent_id
        category = category.parent
        return true if car_category_ids.include?(category.id)
      end

      false
    end

    def vehiculos_product?(product)
      # Define Vehiculos category ID
      vehiculos_category_ids = [3346] # "Vehiculos"

      # Check if product belongs to Vehiculos categories
      return true if vehiculos_category_ids.include?(product.category_id)

      # Check if product belongs to Vehiculos category hierarchy
      category = product.category
      while category&.parent_id
        category = category.parent
        return true if vehiculos_category_ids.include?(category.id)
      end

      false
    end

    def skip_third_party_code_validation?(variant)
      # Skip third_party_code validation for Vehiculos products in Columbia store (ID 46)
      vehiculos_product?(variant.product) && @store&.id == 46
    end

    def third_party_code_validation?(variant)
      ThirdPartyCode.where(store: @store,
                           manufacturer: variant.product.manufacturer,
                           available: true).count.positive?
    end
  end
end
